html {
  height: 100%
}

body {
  margin: 0;
  background-color: #ededed;
  overflow-x: hidden;
  /* 解决el-images为了预览模式设置hidden导致滚动条消失的问题 */
  overflow-y: auto !important;
  padding-right: 0 !important;
  height: 100%;
}

#app {
  height: 100%;
  min-height: 100%;
}

.el-container {
  min-height: 100%;
}

/* 以下为组件公用的样式 */

.box-card {
  width: 100%;
  font-family: Helvetica Neue, PingFang SC, Hiragino Sans GB, Heiti SC, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
}

.header {
  text-align: left;
  font-weight: bolder;
  font-size: 18px;
  color: #666;
  background-color: #FAFAFA;
}

.content {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  text-align: left;
}

.comment {
  font-size: 12px;
  font-weight: normal;
}

.sub-title {
  display: block;
  font-weight: bold;
  margin-bottom: 20px;
}

/* 以下为对Element-UI部分样式的调整 */

.el-main, .el-header, .el-footer {
  padding: 0;
}

.el-main {
  display: flex;
  justify-content: center;
  height: 100%;
  min-height: 100%;
  overflow: hidden;
}

.el-footer {
  height: auto !important;
}

.el-card__header {
  background-color: #FAFAFA;
}

.el-input-number.is-controls-right[class*=small] [class*=decrease], .el-input-number.is-controls-right[class*=small] [class*=increase] {
  line-height: 18px;
}

.el-input--small .el-input__inner {
  height: 39px;
  line-height: 39px;
}

.slide-fade-enter-active {
  transition: all .5s ease;
}

.slide-fade-leave-active {
  transition: all .5s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter, .slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
