[{"id": 8, "title": "凤凰架构：构建可靠的大型分布式系统", "price": 0.0, "rate": 0.0, "description": "<p>这是一部以“如何构建一套可靠的分布式大型软件系统”为叙事主线的开源文档，是一幅帮助开发人员整理现代软件架构各条分支中繁多知识点的技能地图。文章《<a href='https://icyfenix.cn/introduction/about-the-fenix-project.html' target=_blank>什么是“凤凰架构”</a>》详细阐述了这部文档的主旨、目标与名字的来由，文章《<a href='https://icyfenix.cn/exploration/guide/quick-start.html' target=_blank>如何开始</a>》简述了文档每章讨论的主要话题与内容详略分布</p>", "cover": "/static/cover/fenix.png", "detail": "/static/desc/fenix.jpg", "specifications": [{"id": 64, "item": "ISBN", "value": "9787111349662"}, {"id": 69, "item": "装帧", "value": "在线"}, {"id": 66, "item": "页数", "value": "409"}, {"id": 68, "item": "出版年", "value": "2020-6"}, {"id": 65, "item": "书名", "value": "凤凰架构"}, {"id": 68, "item": "副标题", "value": "构建可靠的大型分布式系统"}, {"id": 63, "item": "作者", "value": "周志明"}, {"id": 67, "item": "出版社", "value": "机械工业出版社"}]}, {"id": 1, "title": "深入理解Java虚拟机（第3版）", "price": 129.0, "rate": 9.6, "description": "<p>这是一部从工作原理和工程实践两个维度深入剖析JVM的著作，是计算机领域公认的经典，繁体版在台湾也颇受欢迎。</p><p>自2011年上市以来，前两个版本累计印刷36次，销量超过30万册，两家主要网络书店的评论近90000条，内容上近乎零差评，是原创计算机图书领域不可逾越的丰碑，第3版在第2版的基础上做了重大修订，内容更丰富、实战性更强：根据新版JDK对内容进行了全方位的修订和升级，围绕新技术和生产实践新增逾10万字，包含近50%的全新内容，并对第2版中含糊、瑕疵和错误内容进行了修正。</p><p>全书一共13章，分为五大部分：</p><p>第一部分（第1章）走近Java</p><p>系统介绍了Java的技术体系、发展历程、虚拟机家族，以及动手编译JDK，了解这部分内容能对学习JVM提供良好的指引。</p><p>第二部分（第2~5章）自动内存管理</p><p>详细讲解了Java的内存区域与内存溢出、垃圾收集器与内存分配策略、虚拟机性能监控与故障排除等与自动内存管理相关的内容，以及10余个经典的性能优化案例和优化方法；</p><p>第三部分（第6~9章）虚拟机执行子系统</p><p>深入分析了虚拟机执行子系统，包括类文件结构、虚拟机类加载机制、虚拟机字节码执行引擎，以及多个类加载及其执行子系统的实战案例；</p><p>第四部分（第10~11章）程序编译与代码优化</p><p>详细讲解了程序的前、后端编译与优化，包括前端的易用性优化措施，如泛型、主动装箱拆箱、条件编译等的内容的深入分析；以及后端的性能优化措施，如虚拟机的热点探测方法、HotSpot 的即时编译器、提前编译器，以及各种常见的编译期优化技术；</p><p>第五部分（第12~13章）高效并发</p><p>主要讲解了Java实现高并发的原理，包括Java的内存模型、线程与协程，以及线程安全和锁优化。</p><p>全书以实战为导向，通过大量与实际生产环境相结合的案例分析和展示了解决各种Java技术难题的方案和技巧。</p>", "cover": "/static/cover/jvm3.jpg", "detail": "/static/desc/jvm3.jpg", "specifications": [{"id": 9, "item": "装帧", "value": "平装"}, {"id": 7, "item": "出版社", "value": "机械工业出版社"}, {"id": 2, "item": "副标题", "value": "JVM高级特性与最佳实践"}, {"id": 3, "item": "ISBN", "value": "9787111641247"}, {"id": 4, "item": "书名", "value": "深入理解Java虚拟机（第3版）"}, {"id": 5, "item": "页数", "value": "540"}, {"id": 6, "item": "丛书", "value": "华章原创精品"}, {"id": 8, "item": "出版年", "value": "2019-12"}, {"id": 1, "item": "作者", "value": "周志明"}]}, {"id": 2, "title": "智慧的疆界", "price": 69.0, "rate": 9.1, "description": "<p>这是一部对人工智能充满敬畏之心的匠心之作，由《深入理解Java虚拟机》作者耗时一年完成，它将带你从奠基人物、历史事件、学术理论、研究成果、技术应用等5个维度全面读懂人工智能。</p><p>本书以时间为主线，用专业的知识、通俗的语言、巧妙的内容组织方式，详细讲解了人工智能这个学科的全貌、能解决什么问题、面临怎样的困难、尝试过哪些努力、取得过多少成绩、未来将向何方发展，尽可能消除人工智能的神秘感，把阳春白雪的人工智能从科学的殿堂推向公众面前。</p>", "cover": "/static/cover/ai.jpg", "detail": "/static/desc/ai.jpg", "specifications": [{"id": 16, "item": "出版年", "value": "2018-1-1"}, {"id": 13, "item": "副标题", "value": "从图灵机到人工智能"}, {"id": 14, "item": "页数", "value": "413"}, {"id": 15, "item": "出版社", "value": "机械工业出版社"}, {"id": 12, "item": "书名", "value": "智慧的疆界"}, {"id": 10, "item": "作者", "value": "周志明"}, {"id": 17, "item": "装帧", "value": "平装"}, {"id": 11, "item": "ISBN", "value": "9787111610496"}]}, {"id": 3, "title": "Java虚拟机规范（Java SE 8）", "price": 79.0, "rate": 7.7, "description": "<p>本书完整而准确地阐释了Java虚拟机各方面的细节，围绕Java虚拟机整体架构、编译器、class文件格式、加载、链接与初始化、指令集等核心主题对Java虚拟机进行全面而深入的分析，深刻揭示Java虚拟机的工作原理。同时，书中不仅完整地讲述了由Java SE 8所引入的新特性，例如对包含默认实现代码的接口方法所做的调用，还讲述了为支持类型注解及方法参数注解而对class文件格式所做的扩展，并阐明了class文件中各属性的含义，以及字节码验证的规则。</p>", "cover": "/static/cover/jvms8.jpg", "detail": "", "specifications": [{"id": 18, "item": "作者", "value": "<PERSON> / <PERSON> 等"}, {"id": 24, "item": "出版社", "value": "机械工业出版社"}, {"id": 25, "item": "出版年", "value": "2015-6"}, {"id": 26, "item": "装帧", "value": "平装"}, {"id": 23, "item": "页数", "value": "330"}, {"id": 21, "item": "丛书", "value": "Java核心技术系列"}, {"id": 20, "item": "原作名", "value": "The Java Virtual Machine Specification, Java SE 8 Edition"}, {"id": 19, "item": "译者", "value": "爱飞翔 / 周志明 / 等 "}, {"id": 22, "item": "ISBN", "value": "9787111501596"}]}, {"id": 4, "title": "深入理解Java虚拟机（第2版）", "price": 79.0, "rate": 9.0, "description": "<p>《深入理解Java虚拟机:JVM高级特性与最佳实践(第2版)》内容简介：第1版两年内印刷近10次，4家网上书店的评论近4?000条，98%以上的评论全部为5星级的好评，是整个Java图书领域公认的经典著作和超级畅销书，繁体版在台湾也十分受欢迎。第2版在第1版的基础上做了很大的改进：根据最新的JDK 1.7对全书内容进行了全面的升级和补充；增加了大量处理各种常见JVM问题的技巧和最佳实践；增加了若干与生产环境相结合的实战案例；对第1版中的错误和不足之处的修正；等等。第2版不仅技术更新、内容更丰富，而且实战性更强。</p><p>《深入理解Java虚拟机:JVM高级特性与最佳实践(第2版)》共分为五大部分，围绕内存管理、执行子系统、程序编译与优化、高效并发等核心主题对JVM进行了全面而深入的分析，深刻揭示了JVM的工作原理。</p><p>第一部分从宏观的角度介绍了整个Java技术体系、Java和JVM的发展历程、模块化，以及JDK的编译，这对理解书中后面内容有重要帮助。</p><p>第二部分讲解了JVM的自动内存管理，包括虚拟机内存区域的划分原理以及各种内存溢出异常产生的原因；常见的垃圾收集算法以及垃圾收集器的特点和工作原理；常见虚拟机监控与故障处理工具的原理和使用方法。</p><p>第三部分分析了虚拟机的执行子系统，包括类文件结构、虚拟机类加载机制、虚拟机字节码执行引擎。</p><p>第四部分讲解了程序的编译与代码的优化，阐述了泛型、自动装箱拆箱、条件编译等语法糖的原理；讲解了虚拟机的热点探测方法、HotSpot的即时编译器、编译触发条件，以及如何从虚拟机外部观察和分析JIT编译的数据和结果；</p><p>第五部分探讨了Java实现高效并发的原理，包括JVM内存模型的结构和操作；原子性、可见性和有序性在Java内存模型中的体现；先行发生原则的规则和使用；线程在Java语言中的实现原理；虚拟机实现高效并发所做的一系列锁优化措施。</p>", "cover": "/static/cover/jvm2.jpg", "detail": "/static/desc/jvm2.jpg", "specifications": [{"id": 31, "item": "页数", "value": "433"}, {"id": 32, "item": "丛书", "value": "华章原创精品"}, {"id": 28, "item": "副标题", "value": "JVM高级特性与最佳实践"}, {"id": 29, "item": "ISBN", "value": "9787111421900"}, {"id": 34, "item": "出版年", "value": "2013-9-1"}, {"id": 35, "item": "装帧", "value": "平装"}, {"id": 27, "item": "作者", "value": "周志明"}, {"id": 30, "item": "书名", "value": "深入理解Java虚拟机（第2版）"}, {"id": 33, "item": "出版社", "value": "机械工业出版社"}]}, {"id": 5, "title": "Java虚拟机规范（Java SE 7）", "price": 69.0, "rate": 8.9, "description": "<p>本书整合了自1999年《Java虚拟机规范（第2版）》发布以来Java世界所出现的技术变化。另外，还修正了第2版中的许多错误，以及对目前主流Java虚拟机实现来说已经过时的内容。最后还处理了一些Java虚拟机和Java语言概念的模糊之处。</p><p>2004年发布的Java SE 5.0版为Java语言带来了翻天覆地的变化，但是对Java虚拟机设计的影响则相对较小。在Java SE 7这个版本中，我们扩充了class文件格式以便支持新的Java语言特性，譬如泛型和变长参数方法等。</p>", "cover": "/static/cover/jvms.jpg", "detail": "/static/desc/jvms.jpg", "specifications": [{"id": 41, "item": "页数", "value": "316"}, {"id": 42, "item": "出版社", "value": "机械工业出版社"}, {"id": 36, "item": "作者", "value": "<PERSON> / <PERSON> 等"}, {"id": 37, "item": "译者", "value": "周志明 / 薛笛 / 吴璞渊 / 冶秀刚"}, {"id": 39, "item": "副标题", "value": "从图灵机到人工智能"}, {"id": 45, "item": "装帧", "value": "平装"}, {"id": 44, "item": "出版年", "value": "2014-1"}, {"id": 38, "item": "原作名", "value": "The Java Virtual Machine Specification, Java SE 7 Edition"}, {"id": 43, "item": "丛书", "value": "Java核心技术系列"}, {"id": 40, "item": "ISBN", "value": "9787111445159"}]}, {"id": 6, "title": "深入理解OSGi", "price": 79.0, "rate": 7.7, "description": "<p>本书是原创Java技术图书领域继《深入理解Java虚拟机》后的又一实力之作，也是全球首本基于最新OSGi R5.0规范的著作。理论方面，既全面解读了OSGi规范，深刻揭示了OSGi原理，详细讲解了OSGi服务，又系统地介绍了Equinox框架的使用方法，并通过源码分析了该框架的工作机制；实践方面，不仅包含一些典型的案例，还总结了大量的最佳实践，极具实践指导意义。</p><p>全书共14章，分4个部分。第一部分（第1章）：走近OSGi，主要介绍了什么是OSGi以及为什么要使用OSGi。第二部分（第2～4章）：OSGi规范与原理，对最新的OSGi R5.0中的核心规范进行了全面的解读，首先讲解了OSGi模块的建立、描述、依赖关系的处理，然后讲解了Bundle的启动原理和调度管理，最后讲解了与本地及远程服务相关的内容。第三部分：OSGi服务与Equinox应用实践（第5～11章），不仅详细讲解了OSGi服务纲要规范和企业级规范中最常用的几个子规范和服务的技术细节，还通过一个基于Equinox的BBS案例演示了Equinox的使用方法，最重要的是还通过源码分析了Equinox关键功能的实现机制和原理。第四部分：最佳实践（第12～14章），总结了大量关于OSGi的最佳实践，包括从Bundle如何命名、模块划分、依赖关系处理到保持OSGi动态性、管理程序启动顺序、使用API基线管理模块版本等各方面的实践技巧，此外还介绍了Spring DM的原理以及如何在OSGi环节中进行程序测试。</p>", "cover": "/static/cover/osgi.jpg", "detail": "/static/desc/OSGi.jpg", "specifications": [{"id": 46, "item": "作者", "value": "周志明 / 谢小明 "}, {"id": 49, "item": "书名", "value": "智慧的疆界"}, {"id": 50, "item": "丛书", "value": "华章原创精品"}, {"id": 47, "item": "副标题", "value": "Equinox原理、应用与最佳实践"}, {"id": 53, "item": "出版年", "value": "2013-2-25"}, {"id": 54, "item": "装帧", "value": "平装"}, {"id": 52, "item": "出版社", "value": "机械工业出版社"}, {"id": 48, "item": "ISBN", "value": "9787111408871"}, {"id": 51, "item": "页数", "value": "432"}]}, {"id": 7, "title": "深入理解Java虚拟机", "price": 69.0, "rate": 8.6, "description": "<p>作为一位Java程序员，你是否也曾经想深入理解Java虚拟机，但是却被它的复杂和深奥拒之门外？没关系，本书极尽化繁为简之妙，能带领你在轻松中领略Java虚拟机的奥秘。本书是近年来国内出版的唯一一本与Java虚拟机相关的专著，也是唯一一本同时从核心理论和实际运用这两个角度去探讨Java虚拟机的著作，不仅理论分析得透彻，而且书中包含的典型案例和最佳实践也极具现实指导意义。</p><p>全书共分为五大部分。第一部分从宏观的角度介绍了整个Java技术体系的过去、现在和未来，以及如何独立地编译一个OpenJDK7，这对理解后面的内容很有帮助。第二部分讲解了JVM的自动内存管理，包括虚拟机内存区域的划分原理以及各种内存溢出异常产生的原因；常见的垃圾收集算法以及垃圾收集器的特点和工作原理；常见的虚拟机的监控与调试工具的原理和使用方法。第三部分分析了虚拟机的执行子系统，包括Class的文件结构以及如何存储和访问Class中的数据；虚拟机的类创建机制以及类加载器的工作原理和它对虚拟机的意义；虚拟机字节码的执行引擎以及它在实行代码时涉及的内存结构。第四部分讲解了程序的编译与代码的优化，阐述了泛型、自动装箱拆箱、条件编译等语法糖的原理；讲解了虚拟机的热点探测方法、HotSpot的即时编译器、编译触发条件，以及如何从虚拟机外部观察和分析JIT编译的数据和结果。第五部分探讨了Java实现高效并发的原理，包括JVM内存模型的结构和操作；原子性、可见性和有序性在Java内存模型中的体现；先行发生原则的规则和使用；线程在Java语言中的实现原理；虚拟机实现高效并发所做的一系列锁优化措施。</p>", "cover": "/static/cover/jvm1.jpg", "detail": "", "specifications": [{"id": 55, "item": "作者", "value": "周志明"}, {"id": 59, "item": "页数", "value": "387"}, {"id": 61, "item": "出版年", "value": "2011-6"}, {"id": 60, "item": "出版社", "value": "机械工业出版社"}, {"id": 62, "item": "装帧", "value": "平装"}, {"id": 56, "item": "副标题", "value": "JVM高级特性与最佳实践"}, {"id": 57, "item": "ISBN", "value": "9787111349662"}, {"id": 58, "item": "书名", "value": "深入理解Java虚拟机"}]}]